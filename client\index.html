<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="./public/favicon.png" />

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Royaltea - Fair compensation for game developers" />
    <!-- Prevent caching to ensure fresh content -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <!-- Prevent browser extension interference -->
    <meta name="password-managers" content="ignore" />
    <meta name="form-detection" content="telephone=no" />
    <base href="/" />

    <!-- Early extension error handling script -->
    <script>
      // Immediately handle extension context invalidation errors
      (function() {
        'use strict';

        // Extension error patterns
        const extensionErrorPatterns = [
          /Extension context invalidated/i,
          /chrome-extension:/i,
          /moz-extension:/i,
          /safari-extension:/i,
          /edge-extension:/i,
          /extension.*invalidated/i,
          /context.*invalidated/i,
          /chrome\.runtime/i,
          /browser\.runtime/i,
          /Extension.*disconnected/i,
          /Port.*disconnected/i,
          /ControlLooksLikePasswordCredentialField/i,
          /content_script\.js/i,
          /A listener indicated an asynchronous response by returning true/i,
          /message channel closed before a response was received/i,
          /shouldOfferCompletionListForField/i,
          /keyDownEventHandler/i,
          /runtime\.lastError/i
        ];

        function isExtensionError(message) {
          if (typeof message !== 'string') return false;
          return extensionErrorPatterns.some(pattern => pattern.test(message));
        }

        // Override console methods early
        const originalError = console.error;
        const originalWarn = console.warn;

        console.error = function(...args) {
          const message = args[0];
          if (isExtensionError(message)) {
            return; // Silently ignore extension errors
          }
          originalError.apply(console, args);
        };

        console.warn = function(...args) {
          const message = args[0];
          if (isExtensionError(message)) {
            return; // Silently ignore extension warnings
          }
          originalWarn.apply(console, args);
        };

        // Handle global errors
        window.addEventListener('error', function(event) {
          if (isExtensionError(event.message || event.error?.message || '')) {
            event.preventDefault();
            event.stopPropagation();
            return false;
          }
        }, true);

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
          if (event.reason && isExtensionError(event.reason.message || event.reason.toString())) {
            event.preventDefault();
            return false;
          }
        }, true);

        // Prevent extension scripts from interfering with page load
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
          const element = originalCreateElement.call(this, tagName);

          if (tagName.toLowerCase() === 'script') {
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
              // Block extension scripts
              if (name === 'src' && (
                value.includes('chrome-extension://') ||
                value.includes('moz-extension://') ||
                value.includes('safari-extension://') ||
                value.includes('edge-extension://')
              )) {
                return; // Don't set the src attribute for extension scripts
              }
              return originalSetAttribute.call(this, name, value);
            };
          }

          return element;
        };
      })();
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-S7SFML469V"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-S7SFML469V');
    </script>

    <!-- Start Single Page Apps for GitHub Pages -->
    <script type="text/javascript">
      // This script checks to see if a redirect is present in the query string,
      // converts it back into the correct url and adds it to the
      // browser's history using window.history.replaceState(...),
      // which won't cause the browser to attempt to load the new url.
      // When the single page app is loaded further down in this file,
      // the correct url will be waiting in the browser's history for
      // the single page app to route accordingly.
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) {
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>
    <!-- End Single Page Apps for GitHub Pages -->
    <title>Royaltea - A Game Development Gigwork Platform</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
