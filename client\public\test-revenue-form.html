<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revenue Form Browser Extension Test</title>
    <!-- Prevent browser extension interference -->
    <meta name="password-managers" content="ignore" />
    <meta name="form-detection" content="telephone=no" />
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .amount-input-group {
            display: flex;
            gap: 10px;
        }
        .currency-select {
            width: 120px;
            flex-shrink: 0;
        }
        .amount-input {
            flex: 1;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .error-log {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
        }
        .error-count {
            color: #dc3545;
            font-weight: bold;
        }
        .success-count {
            color: #28a745;
            font-weight: bold;
        }
        /* Prevent browser extension interference with forms */
        input[data-lpignore="true"],
        textarea[data-lpignore="true"],
        select[data-lpignore="true"] {
            -webkit-text-security: none !important;
            background-image: none !important;
            background-color: transparent !important;
        }
        /* Prevent password manager icons from appearing */
        input[data-form-type="financial"]::-webkit-credentials-auto-fill-button,
        input[data-form-type="reference"]::-webkit-credentials-auto-fill-button {
            display: none !important;
            visibility: hidden !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Revenue Form Browser Extension Test</h1>
        <p>This page tests whether browser extension errors are properly handled when interacting with revenue form fields.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="amount">Amount*</label>
                <div class="amount-input-group">
                    <select class="currency-select" data-lpignore="true" data-1p-ignore="true" autocomplete="off">
                        <option value="USD">$ USD</option>
                        <option value="EUR">€ EUR</option>
                        <option value="GBP">£ GBP</option>
                    </select>
                    <input
                        type="text"
                        id="amount"
                        name="amount"
                        class="amount-input"
                        placeholder="0.00"
                        autocomplete="off"
                        data-form-type="financial"
                        data-lpignore="true"
                        data-1p-ignore="true"
                    />
                </div>
            </div>

            <div class="form-group">
                <label for="reference_number">Reference Number</label>
                <input
                    type="text"
                    id="reference_number"
                    name="reference_number"
                    placeholder="Invoice or transaction reference"
                    autocomplete="off"
                    data-form-type="reference"
                    data-lpignore="true"
                    data-1p-ignore="true"
                />
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea
                    id="description"
                    name="description"
                    rows="3"
                    placeholder="Provide details about this revenue entry"
                    autocomplete="off"
                    data-form-type="description"
                    data-lpignore="true"
                    data-1p-ignore="true"
                ></textarea>
            </div>

            <button type="button" class="test-button" onclick="runTest()">🚀 Run Extension Error Test</button>
        </form>

        <div class="error-log">
            <h3>Test Results:</h3>
            <div id="testResults">
                <p>Click "Run Extension Error Test" to start testing...</p>
            </div>
        </div>
    </div>

    <script>
        let errorCount = 0;
        let testStartTime = 0;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        // Extension error patterns
        const extensionErrorPatterns = [
            /ControlLooksLikePasswordCredentialField/i,
            /content_script\.js/i,
            /A listener indicated an asynchronous response by returning true/i,
            /message channel closed before a response was received/i,
            /Cannot read properties of null/i,
            /shouldOfferCompletionListForField/i,
            /keyDownEventHandler/i,
            /runtime\.lastError/i
        ];

        function isExtensionError(message) {
            if (typeof message !== 'string') return false;
            return extensionErrorPatterns.some(pattern => pattern.test(message));
        }

        // Override console methods to track errors
        console.error = function(...args) {
            const message = args[0];
            if (isExtensionError(message)) {
                errorCount++;
                updateTestResults(`❌ Extension Error Detected: ${message}`);
            } else {
                originalConsoleError.apply(console, args);
            }
        };

        console.warn = function(...args) {
            const message = args[0];
            if (isExtensionError(message)) {
                errorCount++;
                updateTestResults(`⚠️ Extension Warning Detected: ${message}`);
            } else {
                originalConsoleWarn.apply(console, args);
            }
        };

        function updateTestResults(message) {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        function runTest() {
            errorCount = 0;
            testStartTime = Date.now();
            const results = document.getElementById('testResults');
            results.innerHTML = '<p>🧪 Starting browser extension error test...</p>';
            
            // Simulate user interactions that typically trigger extension errors
            const testActions = [
                () => {
                    updateTestResults('📝 Testing amount field interaction...');
                    const amountField = document.getElementById('amount');
                    amountField.focus();
                    amountField.value = '1000.50';
                    amountField.blur();
                },
                () => {
                    updateTestResults('📝 Testing reference field interaction...');
                    const refField = document.getElementById('reference_number');
                    refField.focus();
                    refField.value = 'INV-2024-001';
                    refField.blur();
                },
                () => {
                    updateTestResults('📝 Testing description field interaction...');
                    const descField = document.getElementById('description');
                    descField.focus();
                    descField.value = 'Test revenue entry for browser extension compatibility';
                    descField.blur();
                },
                () => {
                    updateTestResults('⌨️ Simulating keyboard events...');
                    const amountField = document.getElementById('amount');
                    amountField.focus();
                    
                    // Simulate typing that often triggers extension errors
                    const events = ['keydown', 'keypress', 'keyup', 'input'];
                    events.forEach(eventType => {
                        const event = new KeyboardEvent(eventType, {
                            key: '5',
                            code: 'Digit5',
                            keyCode: 53,
                            bubbles: true
                        });
                        amountField.dispatchEvent(event);
                    });
                }
            ];

            // Run test actions with delays
            testActions.forEach((action, index) => {
                setTimeout(action, index * 500);
            });

            // Final results after all tests
            setTimeout(() => {
                const testDuration = Date.now() - testStartTime;
                if (errorCount === 0) {
                    updateTestResults(`✅ <span class="success-count">SUCCESS!</span> No browser extension errors detected in ${testDuration}ms`);
                    updateTestResults('🎉 Form fields are properly protected from browser extension interference!');
                } else {
                    updateTestResults(`❌ <span class="error-count">FAILED!</span> ${errorCount} browser extension errors detected in ${testDuration}ms`);
                    updateTestResults('⚠️ Additional protection may be needed for form fields.');
                }
            }, 3000);
        }

        // Also listen for unhandled errors
        window.addEventListener('error', (event) => {
            if (isExtensionError(event.message)) {
                errorCount++;
                updateTestResults(`❌ Unhandled Extension Error: ${event.message}`);
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && isExtensionError(event.reason.message || event.reason.toString())) {
                errorCount++;
                updateTestResults(`❌ Extension Promise Rejection: ${event.reason}`);
            }
        });
    </script>
</body>
</html>
