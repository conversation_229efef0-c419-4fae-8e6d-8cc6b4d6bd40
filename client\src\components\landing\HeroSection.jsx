import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@heroui/react';

/**
 * Hero Section Component - Full Screen Immersive Landing
 * 
 * Features:
 * - Compelling value proposition with clear messaging
 * - Primary call-to-action buttons
 * - Social proof with user count
 * - Smooth scroll indicator
 * - Responsive design for all screen sizes
 */
const HeroSection = ({ onGetStarted, onWatchDemo, onScrollToNext }) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const floatingVariants = {
    animate: {
      y: [0, -10, 0],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Animated Background Shapes - Optimized for performance */}
        <motion.div
          className="absolute top-20 left-20 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.1, 1], // Reduced scale change
            opacity: [0.3, 0.4, 0.3] // Reduced opacity change
          }}
          transition={{
            duration: 12, // Slower animation
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1.1, 1, 1.1], // Reduced scale change
            opacity: [0.2, 0.3, 0.2] // Reduced opacity change
          }}
          transition={{
            duration: 15, // Slower animation
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Floating Particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <motion.div
        className="relative z-10 text-center px-6 max-w-6xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Logo and Title */}
        <motion.div variants={itemVariants} className="mb-8">
          <motion.h1 
            className="text-6xl md:text-8xl font-bold text-white mb-4"
            variants={floatingVariants}
            animate="animate"
          >
            🏰 ROYALTEA
          </motion.h1>
          <motion.div
            className="text-2xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 mb-4"
            variants={itemVariants}
          >
            Keep the Tea. Keep the Creative Power.
          </motion.div>
          <motion.p
            className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto"
            variants={itemVariants}
          >
            Fair, transparent collaboration for creative industries
          </motion.p>
        </motion.div>

        {/* Call to Action Buttons */}
        <motion.div variants={itemVariants} className="mb-12">
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
            <Button
              size="lg"
              color="primary"
              className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
              onPress={onGetStarted}
            >
              Get Started Free
            </Button>
            
            <Button
              size="lg"
              variant="bordered"
              className="w-full sm:w-auto border-2 border-white/30 text-white hover:bg-white/10 font-semibold px-8 py-4 text-lg backdrop-blur-sm transition-all duration-300"
              onPress={onWatchDemo}
            >
              Watch Demo
            </Button>
          </div>
        </motion.div>

        {/* Social Proof */}
        <motion.div variants={itemVariants} className="mb-16">
          <div className="flex items-center justify-center gap-2 text-lg text-gray-300">
            <span className="text-2xl">✨</span>
            <span>Join 2,500+ creators earning fair revenue</span>
          </div>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          variants={itemVariants}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.button
            onClick={onScrollToNext}
            className="flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <span className="text-sm mb-2">Discover Your Path</span>
            <motion.div
              className="text-3xl"
              animate={{
                y: [0, 10, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              ⬇️
            </motion.div>
          </motion.button>
        </motion.div>
      </motion.div>

      {/* Gradient Overlay for Better Text Readability */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/20 pointer-events-none" />
    </div>
  );
};

export default HeroSection;
