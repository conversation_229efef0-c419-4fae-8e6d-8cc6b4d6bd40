import React, { useEffect, useState } from 'react';
import { usePageVisibility } from '../../utils/loading';

/**
 * LoadingAnimation Component
 *
 * A focus-aware loading animation that can be used throughout the application.
 * It displays a spinner with optional text and automatically resets when the page loses focus.
 */
const LoadingAnimation = ({ text = 'Loading...', size = 'medium', fullPage = false }) => {
  // Track page visibility
  const isVisible = usePageVisibility();

  // Track how long the loading animation has been visible
  const [visibleDuration, setVisibleDuration] = useState(0);

  // Determine the size class
  const sizeClass = size === 'small' ? 'spinner-sm' :
                   size === 'large' ? 'spinner-lg' : 'spinner-md';

  // If fullPage is true, center the spinner in the viewport
  const containerClass = fullPage ? 'loading-container-fullpage' : 'loading-container';

  // Track visibility duration with reduced frequency
  useEffect(() => {
    if (!isVisible) {
      // Reset duration when page is not visible
      setVisibleDuration(0);
      return;
    }

    // Start a timer to track how long the loading animation has been visible
    const startTime = Date.now();
    const intervalId = setInterval(() => {
      setVisibleDuration(Date.now() - startTime);
    }, 2000); // Reduced frequency from 1000ms to 2000ms

    return () => {
      clearInterval(intervalId);
    };
  }, [isVisible]);

  // If the page is not visible, don't show the loading animation
  if (!isVisible) {
    return null;
  }

  // If the loading animation has been visible for too long, show a message
  const showTimeoutMessage = visibleDuration > 10000; // 10 seconds

  return (
    <div className={containerClass}>
      <div className={`spinner ${sizeClass}`}></div>
      {text && <div className="loading-text">{text}</div>}
      {showTimeoutMessage && (
        <div className="loading-timeout-message">
          Loading is taking longer than expected. You may refresh the page if it doesn't complete soon.
        </div>
      )}
    </div>
  );
};

export default LoadingAnimation;