import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Avatar, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@heroui/react';
import { Home, User, Settings, LogOut, Menu } from 'lucide-react';

/**
 * Simple Navigation Header
 * 
 * A straightforward navigation header that appears on all pages
 * providing easy access to main sections and user actions.
 */
const SimpleNavHeader = ({ currentUser, onLogout }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const navigationItems = [
    { id: 'home', label: 'Home', path: '/', icon: Home },
    { id: 'start', label: 'Start', path: '/start', icon: '🚀' },
    { id: 'track', label: 'Track', path: '/track', icon: '📊' },
    { id: 'earn', label: 'Earn', path: '/earn', icon: '💰' },
    { id: 'learn', label: 'Learn', path: '/learn', icon: '📚' },
    { id: 'projects', label: 'Projects', path: '/projects', icon: '📁' },
    { id: 'analytics', label: 'Analytics', path: '/analytics', icon: '📈' },
    { id: 'revenue', label: 'Revenue', path: '/revenue', icon: '💵' },
    { id: 'validate', label: 'Validate', path: '/validate', icon: '✅' },
    { id: 'contributions', label: 'Contributions', path: '/contributions', icon: '🤝' },
  ];

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const handleNavigation = (path) => {
    navigate(path);
  };

  const handleUserAction = (action) => {
    switch (action) {
      case 'profile':
        navigate('/profile');
        break;
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        if (onLogout) {
          onLogout();
        }
        break;
      default:
        break;
    }
  };

  return (
    <header className="sticky top-0 z-50 bg-black/80 backdrop-blur-md border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo/Brand */}
          <div className="flex items-center gap-3">
            <button
              onClick={() => navigate('/')}
              className="flex items-center gap-2 text-white hover:text-purple-400 transition-colors"
            >
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <span className="font-bold text-lg hidden sm:block">Royaltea</span>
            </button>
          </div>

          {/* Navigation Items - Desktop */}
          <nav className="hidden md:flex items-center gap-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <Button
                  key={item.id}
                  variant={active ? "solid" : "light"}
                  color={active ? "primary" : "default"}
                  size="sm"
                  className={`
                    ${active 
                      ? 'bg-purple-600 text-white' 
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                    }
                    transition-all duration-200
                  `}
                  onClick={() => handleNavigation(item.path)}
                  startContent={
                    typeof Icon === 'string' ? (
                      <span className="text-sm">{Icon}</span>
                    ) : (
                      <Icon size={16} />
                    )
                  }
                >
                  {item.label}
                </Button>
              );
            })}
          </nav>

          {/* User Menu */}
          <div className="flex items-center gap-3">
            {currentUser ? (
              <Dropdown placement="bottom-end">
                <DropdownTrigger>
                  <Button
                    variant="light"
                    className="p-0 min-w-0 h-auto"
                  >
                    <Avatar
                      src={currentUser.avatar_url}
                      name={currentUser.display_name || currentUser.email}
                      size="sm"
                      className="cursor-pointer"
                    />
                  </Button>
                </DropdownTrigger>
                <DropdownMenu
                  aria-label="User menu"
                  onAction={handleUserAction}
                >
                  <DropdownItem
                    key="profile"
                    startContent={<User size={16} />}
                  >
                    Profile
                  </DropdownItem>
                  <DropdownItem
                    key="settings"
                    startContent={<Settings size={16} />}
                  >
                    Settings
                  </DropdownItem>
                  <DropdownItem
                    key="logout"
                    color="danger"
                    startContent={<LogOut size={16} />}
                  >
                    Logout
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            ) : (
              <Button
                color="primary"
                size="sm"
                onClick={() => navigate('/login')}
              >
                Login
              </Button>
            )}

            {/* Mobile Menu */}
            <div className="md:hidden">
              <Dropdown placement="bottom-end">
                <DropdownTrigger>
                  <Button
                    variant="light"
                    size="sm"
                    isIconOnly
                    className="text-white"
                  >
                    <Menu size={20} />
                  </Button>
                </DropdownTrigger>
                <DropdownMenu
                  aria-label="Navigation menu"
                  onAction={(key) => {
                    const item = navigationItems.find(item => item.id === key);
                    if (item) {
                      handleNavigation(item.path);
                    }
                  }}
                >
                  {navigationItems.map((item) => (
                    <DropdownItem
                      key={item.id}
                      startContent={
                        typeof item.icon === 'string' ? (
                          <span className="text-sm">{item.icon}</span>
                        ) : (
                          <item.icon size={16} />
                        )
                      }
                    >
                      {item.label}
                    </DropdownItem>
                  ))}
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
        </div>
      </div>

      {/* Current Page Indicator */}
      <div className="bg-white/5 px-4 py-2">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-2 text-sm text-white/60">
            <Home size={14} />
            <span>/</span>
            <span className="text-white">
              {navigationItems.find(item => isActive(item.path))?.label || 'Page'}
            </span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default SimpleNavHeader;
