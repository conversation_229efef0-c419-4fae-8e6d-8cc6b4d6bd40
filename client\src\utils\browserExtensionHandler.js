/**
 * Browser Extension Error Handler
 * 
 * This utility helps suppress and handle errors caused by browser extensions
 * (particularly password managers and autofill extensions) that interfere
 * with form fields and cause console errors.
 */

// List of error patterns that are typically caused by browser extensions
const EXTENSION_ERROR_PATTERNS = [
  /ControlLooksLikePasswordCredentialField/i,
  /content_script\.js/i,
  /A listener indicated an asynchronous response by returning true/i,
  /message channel closed before a response was received/i,
  /Cannot read properties of null/i,
  /shouldOfferCompletionListForField/i,
  /keyDownEventHandler/i,
  /runtime\.lastError/i,
  /Extension context invalidated/i,
  /chrome-extension:\/\//i,
  /moz-extension:\/\//i,
  /safari-extension:\/\//i,
  /edge-extension:\/\//i,
  /extension.*invalidated/i,
  /context.*invalidated/i,
  /chrome\.runtime/i,
  /browser\.runtime/i,
  /Extension.*disconnected/i,
  /Port.*disconnected/i
];

// Store original console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

/**
 * Check if an error message matches known browser extension error patterns
 * @param {string} message - The error message to check
 * @returns {boolean} - True if the error is likely from a browser extension
 */
export function isExtensionError(message) {
  if (typeof message !== 'string') return false;

  return EXTENSION_ERROR_PATTERNS.some(pattern => pattern.test(message));
}

/**
 * Enhanced console.error that filters out browser extension errors
 * @param {...any} args - Console arguments
 */
function filteredConsoleError(...args) {
  const message = args[0];
  
  // If it's an extension error, log it as a debug message instead
  if (isExtensionError(message)) {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[Browser Extension]', ...args);
    }
    return;
  }
  
  // Otherwise, use the original console.error
  originalConsoleError.apply(console, args);
}

/**
 * Enhanced console.warn that filters out browser extension warnings
 * @param {...any} args - Console arguments
 */
function filteredConsoleWarn(...args) {
  const message = args[0];
  
  // If it's an extension warning, log it as a debug message instead
  if (isExtensionError(message)) {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[Browser Extension Warning]', ...args);
    }
    return;
  }
  
  // Otherwise, use the original console.warn
  originalConsoleWarn.apply(console, args);
}

/**
 * Initialize the browser extension error handler
 * This replaces console.error and console.warn with filtered versions
 */
export function initBrowserExtensionHandler() {
  // Apply filtering in all environments to prevent extension errors from cluttering console
  console.error = filteredConsoleError;
  console.warn = filteredConsoleWarn;

  // Handle unhandled promise rejections that might be extension-related
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && isExtensionError(event.reason.message || event.reason.toString())) {
      event.preventDefault(); // Prevent the error from being logged
      if (process.env.NODE_ENV === 'development') {
        console.debug('[Browser Extension Promise Rejection]', event.reason);
      }
    }
  });

  // Handle general errors
  window.addEventListener('error', (event) => {
    if (isExtensionError(event.message || event.error?.message || '')) {
      event.preventDefault(); // Prevent the error from being logged
      if (process.env.NODE_ENV === 'development') {
        console.debug('[Browser Extension Error]', event.error || event.message);
      }
    }
  });

  // Handle extension context invalidation errors specifically
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (typeof listener === 'function') {
      const wrappedListener = function(...args) {
        try {
          return listener.apply(this, args);
        } catch (error) {
          if (isExtensionError(error.message || error.toString())) {
            if (process.env.NODE_ENV === 'development') {
              console.debug('[Browser Extension Event Error]', error);
            }
            return; // Silently ignore extension errors
          }
          throw error; // Re-throw non-extension errors
        }
      };
      return originalAddEventListener.call(this, type, wrappedListener, options);
    }
    return originalAddEventListener.call(this, type, listener, options);
  };

  // Override fetch to handle extension-related network errors
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    return originalFetch.apply(this, args).catch(error => {
      if (isExtensionError(error.message || error.toString())) {
        if (process.env.NODE_ENV === 'development') {
          console.debug('[Browser Extension Fetch Error]', error);
        }
        // Return a rejected promise with a generic network error instead
        return Promise.reject(new Error('Network request failed'));
      }
      throw error; // Re-throw non-extension errors
    });
  };
}

/**
 * Restore original console methods
 */
export function restoreBrowserExtensionHandler() {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
}

/**
 * Add form field attributes to prevent browser extension interference
 * @param {HTMLElement} element - The form element to protect
 */
export function protectFormField(element) {
  if (!element) return;
  
  // Add attributes to prevent password managers from interfering
  element.setAttribute('data-lpignore', 'true');
  element.setAttribute('data-1p-ignore', 'true');
  element.setAttribute('autocomplete', 'off');
  
  // Add specific data attributes based on field type
  if (element.type === 'text' && element.name) {
    if (element.name.includes('amount') || element.name.includes('price')) {
      element.setAttribute('data-form-type', 'financial');
    } else if (element.name.includes('reference') || element.name.includes('id')) {
      element.setAttribute('data-form-type', 'reference');
    } else if (element.name.includes('description') || element.name.includes('note')) {
      element.setAttribute('data-form-type', 'description');
    }
  }
}

/**
 * Protect all form fields in a container from browser extension interference
 * @param {HTMLElement} container - The container element to scan for form fields
 */
export function protectAllFormFields(container = document) {
  const formFields = container.querySelectorAll('input, textarea, select');
  formFields.forEach(protectFormField);
}

// Default export
export default {
  initBrowserExtensionHandler,
  restoreBrowserExtensionHandler,
  protectFormField,
  protectAllFormFields,
  isExtensionError
};
