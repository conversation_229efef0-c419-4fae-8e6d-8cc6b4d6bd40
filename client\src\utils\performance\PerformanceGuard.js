// Performance Guard Utility
// Monitors and prevents performance issues that could cause unresponsiveness

class PerformanceGuard {
  constructor() {
    this.timers = new Set();
    this.intervals = new Set();
    this.animationFrames = new Set();
    this.eventListeners = new Map();
    this.performanceWarnings = [];
    this.isMonitoring = false;
    this.frameCount = 0;
    this.lastFrameTime = performance.now();
    this.fpsHistory = [];
    
    // Performance thresholds
    this.thresholds = {
      maxTimers: 20,
      maxIntervals: 10,
      maxEventListeners: 50,
      minFPS: 30,
      maxMemoryMB: 100
    };
  }

  // Start monitoring performance
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.monitorFPS();
    this.monitorMemory();
    this.overrideTimerMethods();
    this.overrideEventListeners();
    
    console.log('🛡️ Performance Guard activated');
  }

  // Stop monitoring
  stopMonitoring() {
    this.isMonitoring = false;
    this.restoreOriginalMethods();
    console.log('🛡️ Performance Guard deactivated');
  }

  // Monitor FPS
  monitorFPS() {
    const measureFPS = (currentTime) => {
      if (!this.isMonitoring) return;
      
      this.frameCount++;
      const deltaTime = currentTime - this.lastFrameTime;
      
      if (deltaTime >= 1000) { // Every second
        const fps = Math.round((this.frameCount * 1000) / deltaTime);
        this.fpsHistory.push(fps);
        
        // Keep only last 10 measurements
        if (this.fpsHistory.length > 10) {
          this.fpsHistory.shift();
        }
        
        // Check for performance issues
        if (fps < this.thresholds.minFPS) {
          this.addWarning('LOW_FPS', `FPS dropped to ${fps}`);
        }
        
        this.frameCount = 0;
        this.lastFrameTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
  }

  // Monitor memory usage
  monitorMemory() {
    if (!performance.memory) return;
    
    const checkMemory = () => {
      if (!this.isMonitoring) return;
      
      const memoryMB = performance.memory.usedJSHeapSize / (1024 * 1024);
      
      if (memoryMB > this.thresholds.maxMemoryMB) {
        this.addWarning('HIGH_MEMORY', `Memory usage: ${memoryMB.toFixed(1)}MB`);
      }
      
      setTimeout(checkMemory, 5000); // Check every 5 seconds
    };
    
    setTimeout(checkMemory, 5000);
  }

  // Override timer methods to track them
  overrideTimerMethods() {
    // Store original methods
    this.originalSetTimeout = window.setTimeout;
    this.originalSetInterval = window.setInterval;
    this.originalClearTimeout = window.clearTimeout;
    this.originalClearInterval = window.clearInterval;
    this.originalRequestAnimationFrame = window.requestAnimationFrame;
    this.originalCancelAnimationFrame = window.cancelAnimationFrame;

    // Override setTimeout
    window.setTimeout = (callback, delay, ...args) => {
      const id = this.originalSetTimeout(callback, delay, ...args);
      this.timers.add(id);
      
      if (this.timers.size > this.thresholds.maxTimers) {
        this.addWarning('TOO_MANY_TIMERS', `${this.timers.size} active timers`);
      }
      
      return id;
    };

    // Override setInterval
    window.setInterval = (callback, delay, ...args) => {
      const id = this.originalSetInterval(callback, delay, ...args);
      this.intervals.add(id);
      
      if (this.intervals.size > this.thresholds.maxIntervals) {
        this.addWarning('TOO_MANY_INTERVALS', `${this.intervals.size} active intervals`);
      }
      
      return id;
    };

    // Override clearTimeout
    window.clearTimeout = (id) => {
      this.timers.delete(id);
      return this.originalClearTimeout(id);
    };

    // Override clearInterval
    window.clearInterval = (id) => {
      this.intervals.delete(id);
      return this.originalClearInterval(id);
    };

    // Override requestAnimationFrame
    window.requestAnimationFrame = (callback) => {
      const id = this.originalRequestAnimationFrame(callback);
      this.animationFrames.add(id);
      return id;
    };

    // Override cancelAnimationFrame
    window.cancelAnimationFrame = (id) => {
      this.animationFrames.delete(id);
      return this.originalCancelAnimationFrame(id);
    };
  }

  // Override addEventListener to track event listeners
  overrideEventListeners() {
    this.originalAddEventListener = EventTarget.prototype.addEventListener;
    this.originalRemoveEventListener = EventTarget.prototype.removeEventListener;

    EventTarget.prototype.addEventListener = function(type, listener, options) {
      const key = `${this.constructor.name}_${type}`;
      const listeners = performanceGuard.eventListeners.get(key) || new Set();
      listeners.add(listener);
      performanceGuard.eventListeners.set(key, listeners);
      
      const totalListeners = Array.from(performanceGuard.eventListeners.values())
        .reduce((total, set) => total + set.size, 0);
      
      if (totalListeners > performanceGuard.thresholds.maxEventListeners) {
        performanceGuard.addWarning('TOO_MANY_LISTENERS', `${totalListeners} event listeners`);
      }
      
      return performanceGuard.originalAddEventListener.call(this, type, listener, options);
    };

    EventTarget.prototype.removeEventListener = function(type, listener, options) {
      const key = `${this.constructor.name}_${type}`;
      const listeners = performanceGuard.eventListeners.get(key);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          performanceGuard.eventListeners.delete(key);
        }
      }
      
      return performanceGuard.originalRemoveEventListener.call(this, type, listener, options);
    };
  }

  // Restore original methods
  restoreOriginalMethods() {
    if (this.originalSetTimeout) {
      window.setTimeout = this.originalSetTimeout;
      window.setInterval = this.originalSetInterval;
      window.clearTimeout = this.originalClearTimeout;
      window.clearInterval = this.originalClearInterval;
      window.requestAnimationFrame = this.originalRequestAnimationFrame;
      window.cancelAnimationFrame = this.originalCancelAnimationFrame;
      
      EventTarget.prototype.addEventListener = this.originalAddEventListener;
      EventTarget.prototype.removeEventListener = this.originalRemoveEventListener;
    }
  }

  // Add performance warning
  addWarning(type, message) {
    const warning = {
      type,
      message,
      timestamp: Date.now(),
      stack: new Error().stack
    };
    
    this.performanceWarnings.push(warning);
    
    // Keep only last 50 warnings
    if (this.performanceWarnings.length > 50) {
      this.performanceWarnings.shift();
    }
    
    console.warn(`⚠️ Performance Warning [${type}]: ${message}`);
    
    // Auto-cleanup if too many issues
    if (type === 'TOO_MANY_INTERVALS' && this.intervals.size > this.thresholds.maxIntervals * 2) {
      this.emergencyCleanup();
    }
  }

  // Emergency cleanup
  emergencyCleanup() {
    console.warn('🚨 Emergency performance cleanup initiated');
    
    // Clear excessive intervals
    this.intervals.forEach(id => {
      this.originalClearInterval(id);
    });
    this.intervals.clear();
    
    // Clear excessive timers
    this.timers.forEach(id => {
      this.originalClearTimeout(id);
    });
    this.timers.clear();
    
    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  // Get performance report
  getReport() {
    const avgFPS = this.fpsHistory.length > 0 
      ? Math.round(this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length)
      : 0;
    
    const memoryMB = performance.memory 
      ? Math.round(performance.memory.usedJSHeapSize / (1024 * 1024))
      : 0;
    
    const totalListeners = Array.from(this.eventListeners.values())
      .reduce((total, set) => total + set.size, 0);

    return {
      timers: this.timers.size,
      intervals: this.intervals.size,
      animationFrames: this.animationFrames.size,
      eventListeners: totalListeners,
      avgFPS,
      memoryMB,
      warnings: this.performanceWarnings.length,
      recentWarnings: this.performanceWarnings.slice(-5)
    };
  }
}

// Create global instance
const performanceGuard = new PerformanceGuard();

// Auto-start in development
if (process.env.NODE_ENV === 'development') {
  performanceGuard.startMonitoring();
}

export default performanceGuard;
