const { chromium } = require('playwright');

// Test credentials
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Browser extension error patterns to detect
const EXTENSION_ERROR_PATTERNS = [
  /ControlLooksLikePasswordCredentialField/i,
  /content_script\.js/i,
  /A listener indicated an asynchronous response by returning true/i,
  /message channel closed before a response was received/i,
  /Cannot read properties of null/i,
  /shouldOfferCompletionListForField/i,
  /keyDownEventHandler/i,
  /runtime\.lastError/i
];

function isExtensionError(message) {
  if (typeof message !== 'string') return false;
  return EXTENSION_ERROR_PATTERNS.some(pattern => pattern.test(message));
}

async function testRevenuePageErrors() {
  console.log('🧪 Testing Revenue Page for Browser Extension Errors');
  console.log('='.repeat(60));

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 500
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  // Track console errors
  const consoleErrors = [];
  const extensionErrors = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error' || msg.type() === 'warning') {
      const message = msg.text();
      consoleErrors.push(message);
      
      if (isExtensionError(message)) {
        extensionErrors.push(message);
        console.log(`❌ Extension Error: ${message}`);
      }
    }
  });

  // Track page errors
  page.on('pageerror', error => {
    const message = error.message;
    consoleErrors.push(message);
    
    if (isExtensionError(message)) {
      extensionErrors.push(message);
      console.log(`❌ Page Extension Error: ${message}`);
    }
  });

  try {
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173');
    await page.waitForLoadState('networkidle');

    // Check if we're on login page
    const isLoginPage = await page.locator('input[type="email"]').isVisible({ timeout: 5000 });
    
    if (isLoginPage) {
      console.log('🔐 Logging in with test credentials...');
      
      await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
      await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
      
      // Click login button
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
    }

    // Navigate to a project's revenue page
    console.log('📊 Navigating to revenue page...');
    
    // Try to find a project link or navigate directly
    const projectLinks = await page.locator('a[href*="/project/"]').count();
    
    if (projectLinks > 0) {
      // Click on first project
      await page.locator('a[href*="/project/"]').first().click();
      await page.waitForLoadState('networkidle');
      
      // Look for revenue link
      const revenueLink = page.locator('a[href*="/revenue"]').first();
      if (await revenueLink.isVisible({ timeout: 5000 })) {
        await revenueLink.click();
        await page.waitForLoadState('networkidle');
      }
    } else {
      // Try direct navigation to revenue page
      await page.goto('http://localhost:5173/revenue');
      await page.waitForLoadState('networkidle');
    }

    console.log('⏳ Waiting for page to fully load...');
    await page.waitForTimeout(5000);

    // Test form interactions that typically trigger extension errors
    console.log('📝 Testing form field interactions...');
    
    // Look for amount input field
    const amountField = page.locator('input[name="amount"], input[id="amount"]').first();
    if (await amountField.isVisible({ timeout: 5000 })) {
      console.log('  💰 Testing amount field...');
      await amountField.focus();
      await amountField.fill('1000.50');
      await page.waitForTimeout(1000);
      await amountField.blur();
    }

    // Look for reference number field
    const referenceField = page.locator('input[name="reference_number"], input[id="reference_number"]').first();
    if (await referenceField.isVisible({ timeout: 5000 })) {
      console.log('  📄 Testing reference field...');
      await referenceField.focus();
      await referenceField.fill('INV-2024-001');
      await page.waitForTimeout(1000);
      await referenceField.blur();
    }

    // Look for description field
    const descriptionField = page.locator('textarea[name="description"], textarea[id="description"]').first();
    if (await descriptionField.isVisible({ timeout: 5000 })) {
      console.log('  📝 Testing description field...');
      await descriptionField.focus();
      await descriptionField.fill('Test revenue entry for browser extension compatibility');
      await page.waitForTimeout(1000);
      await descriptionField.blur();
    }

    // Additional keyboard interactions
    console.log('⌨️ Testing keyboard interactions...');
    if (await amountField.isVisible()) {
      await amountField.focus();
      await page.keyboard.press('Backspace');
      await page.keyboard.type('2500.75');
      await page.waitForTimeout(1000);
    }

    // Wait for any delayed errors
    console.log('⏳ Waiting for potential delayed errors...');
    await page.waitForTimeout(5000);

    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/revenue-page-test.png', 
      fullPage: true 
    });

    // Results
    console.log('\n📊 Test Results:');
    console.log('='.repeat(40));
    console.log(`Total Console Messages: ${consoleErrors.length}`);
    console.log(`Extension Errors Detected: ${extensionErrors.length}`);
    
    if (extensionErrors.length === 0) {
      console.log('✅ SUCCESS: No browser extension errors detected!');
      console.log('🎉 Revenue page form fields are properly protected.');
    } else {
      console.log('❌ FAILED: Browser extension errors were detected:');
      extensionErrors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    // List all console messages for debugging
    if (consoleErrors.length > 0) {
      console.log('\n📋 All Console Messages:');
      consoleErrors.forEach((error, index) => {
        const isExtension = isExtensionError(error);
        const prefix = isExtension ? '❌' : 'ℹ️';
        console.log(`  ${prefix} ${index + 1}. ${error}`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    await browser.close();
  }

  return {
    totalErrors: consoleErrors.length,
    extensionErrors: extensionErrors.length,
    success: extensionErrors.length === 0
  };
}

// Run the test
if (require.main === module) {
  testRevenuePageErrors()
    .then(results => {
      console.log('\n🏁 Test Complete');
      process.exit(results.success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { testRevenuePageErrors };
